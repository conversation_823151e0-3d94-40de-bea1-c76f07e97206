defmodule Repobot.SourceFiles.FormComponent.Save do
  use Repobot.Operation, type: :form

  require Repobot.SourceFile
  alias Repobot.{SourceFiles, Tags, SourceFile}

  schema(SourceFile,
    accept: [
      :name,
      :content,
      :target_path,
      :user_id,
      :organization_id,
      :is_template,
      :template_vars
    ],
    default_presence: :optional
  )

  @impl true
  def prepare(context) do
    action = Map.get(context, :action)
    current_user = Map.get(context, :current_user)

    # For :edit action, ensure we have the current user preloaded for tag operations
    updated_context =
      case action do
        :edit when not is_nil(current_user) ->
          # Ensure we have the current user for tag operations
          user_with_org = Repobot.Repo.preload(current_user, :default_organization)
          Map.put(context, :current_user, user_with_org)

        _ ->
          context
      end

    {:ok, updated_context}
  end

  @impl true
  def execute(context) do
    changeset = cast_changeset(context)

    action = Map.get(context, :action)
    source_file = Map.get(context, :source_file)

    IO.puts("=== SAVE EXECUTE CALLED ===")
    IO.inspect(action, label: "Action")
    IO.inspect(changeset.changes, label: "Changeset changes")

    case action do
      :new ->
        # Get content from context since it's a virtual field
        content = Map.get(context, :content)
        IO.puts("=== CONTENT EXTRACTED ===")
        IO.inspect(content, label: "Content")

        case SourceFiles.create_source_file_from_changeset(changeset, content) do
          {:ok, source_file} -> {:ok, source_file}
          {:error, changeset} -> {:error, changeset}
        end

      :edit ->
        # Check if source file is read-only
        if source_file && source_file.read_only do
          {:error, "Cannot update read-only source file"}
        else
          # Get content from context since it's a virtual field
          content = Map.get(context, :content)

          case SourceFiles.update_source_file_from_changeset(source_file, changeset, content) do
            {:ok, source_file} -> {:ok, source_file}
            {:error, changeset} -> {:error, changeset}
          end
        end
    end
  end

  defp cast_changeset(context) do
    changeset = Map.get(context, :changeset)
    tag_names = Map.get(context, :tag_names, [])
    current_user = Map.get(context, :current_user)

    IO.puts("=== CAST_CHANGESET CALLED ===")
    IO.inspect(tag_names, label: "Tag names")
    IO.inspect(current_user, label: "Current user")

    # Handle tags association
    updated_changeset =
      if length(tag_names) > 0 and not is_nil(current_user) do
        IO.puts("Processing tags...")
        tags = Tags.get_or_create_tags(tag_names, current_user)
        IO.inspect(tags, label: "Created/found tags")
        result = Ecto.Changeset.put_assoc(changeset, :tags, tags)
        IO.inspect(result.changes, label: "Updated changeset changes")
        result
      else
        IO.puts("No tags to process")
        changeset
      end

    updated_changeset
  end
end
